/* IR3 V2 Parameters Showcase Section */
/* File: assets/ir3-parameters-showcase.css */

/* Parameters Section Base Styles */
.parameters-section {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background: #000;
  background-image: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #0a0a0a 100%);
}

/* Background Layers */
.background-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, #1a1a2e 0%, #0a0a0a 50%, #000 100%);
  opacity: 0.8;
}

.animated-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(74, 144, 226, 0.1) 50%,
    transparent 70%
  );
  animation: gradientShift 8s ease-in-out infinite;
}

.grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(74, 144, 226, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(74, 144, 226, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.3;
}

.tech-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(90deg, transparent 0%, rgba(74, 144, 226, 0.2) 50%, transparent 100%);
  animation: techLinesMove 6s linear infinite;
}

.floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(74, 144, 226, 0.1), rgba(142, 68, 173, 0.1));
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

/* Content Layer */
.content-layer {
  position: relative;
  z-index: 2;
  padding: 20px 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 40px;
  width: 100%;
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: 25px;
  flex-shrink: 0;
}

.pre-title {
  display: inline-block;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  color: #4a90e2;
  background: linear-gradient(90deg, #4a90e2, #8e44ad);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 16px;
}

.main-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 24px;
  background: linear-gradient(90deg, #ffffff, #4a90e2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-description {
  font-size: 1.2rem;
  color: #b0b0b0;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Parameters Navigation Tabs */
.parameters-nav {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 25px;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.parameter-tab {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(74, 144, 226, 0.3);
  border-radius: 12px;
  padding: 12px 24px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

.parameter-tab:hover {
  border-color: rgba(74, 144, 226, 0.6);
  background: rgba(74, 144, 226, 0.2);
  transform: translateY(-2px);
}

.parameter-tab.active {
  background: linear-gradient(135deg, #4a90e2, #8e44ad);
  border-color: #4a90e2;
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

/* Parameters Content */
.parameters-content {
  width: 100%;
  max-width: 100%;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.parameter-category {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(74, 144, 226, 0.2);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: none;
  width: 100%;
  flex: 1;
}

.parameter-category.active {
  display: flex;
  flex-direction: column;
}

/* Parameter category styles are now handled above in the new layout */

.parameter-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #4a90e2, #8e44ad, #e74c3c);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.parameter-category:hover::before {
  transform: translateX(0);
}

.parameter-category:hover {
  transform: translateY(-5px);
  border-color: rgba(74, 144, 226, 0.4);
  box-shadow: 0 20px 40px rgba(74, 144, 226, 0.1);
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.category-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(45deg, #4a90e2, #8e44ad);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.category-header h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.parameter-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px 30px;
  margin-top: 20px;
}

.parameter-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  flex-wrap: wrap;
  gap: 8px;
}

.parameter-item:hover {
  transform: translateX(5px);
  background: rgba(255, 255, 255, 0.05);
  padding-left: 10px;
  border-radius: 8px;
}

.parameter-item:last-child {
  border-bottom: none;
}

.param-name {
  font-size: 0.95rem;
  color: #b0b0b0;
  font-weight: 500;
}

.param-value {
  font-size: 0.95rem;
  color: #ffffff;
  font-weight: 600;
  text-align: right;
}

.param-value.highlight {
  background: linear-gradient(90deg, #4a90e2, #8e44ad);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.param-value.supported {
  color: #27ae60;
}



/* Animations */
@keyframes gradientShift {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

@keyframes techLinesMove {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(120deg); }
  66% { transform: translateY(10px) rotate(240deg); }
}

.animate-fade-in {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards;
}

.animate-fade-in[data-delay="0.2"] {
  animation-delay: 0.2s;
}

.animate-fade-in[data-delay="0.4"] {
  animation-delay: 0.4s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .parameters-section {
    height: 100vh;
    overflow: hidden;
    padding: 20px 20px;
  }

  .parameters-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 20px;
  }

  .parameter-category {
    padding: 12px;
    margin-bottom: 0;
  }

  .category-header {
    margin-bottom: 12px;
  }

  .category-header h3 {
    font-size: 1.1rem;
    margin-bottom: 6px;
  }

  .parameter-item {
    padding: 6px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    gap: 8px;
  }

  .param-name {
    font-size: 0.8rem;
    flex: 1;
    text-align: left;
  }

  .param-value {
    font-size: 0.75rem;
    flex-shrink: 0;
    text-align: right;
    max-width: 50%;
  }
}

@media (max-width: 768px) {
  .parameters-section {
    height: 100vh;
    overflow: hidden;
    padding: 15px 16px;
  }

  .main-title {
    font-size: 1.8rem;
    line-height: 1.2;
    margin-bottom: 12px;
  }

  .section-description {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 20px;
  }

  .parameters-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 15px;
  }

  .parameter-category {
    padding: 10px;
    border-radius: 10px;
  }

  .category-header {
    margin-bottom: 10px;
  }

  .category-header h3 {
    font-size: 1rem;
    margin-bottom: 4px;
  }

  .category-icon {
    width: 32px;
    height: 32px;
    margin-right: 10px;
  }

  .parameter-item {
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    gap: 6px;
  }

  .param-name {
    font-size: 0.75rem;
    flex: 1;
    text-align: left;
  }

  .param-value {
    font-size: 0.7rem;
    flex-shrink: 0;
    text-align: right;
    max-width: 50%;
  }

  .content-layer {
    padding: 0;
  }

  .section-header {
    margin-bottom: 20px;
    text-align: center;
  }

  .container {
    padding: 0;
  }
}

@media (max-width: 480px) {
  .parameters-section {
    height: 100vh;
    overflow: hidden;
    padding: 10px 12px;
  }

  .main-title {
    font-size: 1.6rem;
    margin-bottom: 8px;
  }

  .section-description {
    font-size: 0.8rem;
    line-height: 1.3;
    margin-bottom: 15px;
  }

  .parameters-grid {
    grid-template-columns: 1fr;
    gap: 8px;
    margin-bottom: 10px;
  }

  .parameter-category {
    padding: 8px;
    border-radius: 8px;
  }

  .category-header {
    margin-bottom: 8px;
  }

  .category-header h3 {
    font-size: 0.9rem;
    margin-bottom: 4px;
  }

  .category-icon {
    width: 28px;
    height: 28px;
    margin-right: 8px;
  }

  .parameter-item {
    padding: 4px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    gap: 4px;
  }

  .param-name {
    font-size: 0.7rem;
    flex: 1;
    text-align: left;
  }

  .param-value {
    font-size: 0.65rem;
    flex-shrink: 0;
    text-align: right;
    max-width: 50%;
  }

  .container {
    padding: 0;
  }
}
