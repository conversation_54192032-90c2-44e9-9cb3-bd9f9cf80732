{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-01T08:12:48.900Z", "updatedAt": "2025-08-01T08:12:48.904Z", "resourceCount": 3}, "resources": [{"id": "shopify-development-workflow", "source": "project", "protocol": "execution", "name": "Shopify Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/shopify-expert/execution/shopify-development-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T08:12:48.902Z", "updatedAt": "2025-08-01T08:12:48.902Z", "scannedAt": "2025-08-01T08:12:48.902Z", "path": "role/shopify-expert/execution/shopify-development-workflow.execution.md"}}, {"id": "shopify-expert", "source": "project", "protocol": "role", "name": "Shopify Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/shopify-expert/shopify-expert.role.md", "metadata": {"createdAt": "2025-08-01T08:12:48.903Z", "updatedAt": "2025-08-01T08:12:48.903Z", "scannedAt": "2025-08-01T08:12:48.903Z", "path": "role/shopify-expert/shopify-expert.role.md"}}, {"id": "shopify-development-thinking", "source": "project", "protocol": "thought", "name": "Shopify Development Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/shopify-expert/thought/shopify-development-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T08:12:48.903Z", "updatedAt": "2025-08-01T08:12:48.903Z", "scannedAt": "2025-08-01T08:12:48.903Z", "path": "role/shopify-expert/thought/shopify-development-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "role": 1, "thought": 1}, "bySource": {"project": 3}}}