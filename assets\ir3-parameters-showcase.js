// IR3 V2 Parameters Showcase Section
// File: assets/ir3-parameters-showcase.js

document.addEventListener('DOMContentLoaded', function() {
  console.log('🔧 IR3 Parameters Showcase initialized');

  // Initialize animations
  initializeAnimations();
  
  // Initialize parameter interactions
  initializeParameterInteractions();
  
  // Initialize scroll animations
  initializeScrollAnimations();

  // Initialize parameter tabs
  initializeParameterTabs();
});

function initializeAnimations() {
  // Animate parameter categories on load
  const categories = document.querySelectorAll('.parameter-category');
  
  categories.forEach((category, index) => {
    category.style.opacity = '0';
    category.style.transform = 'translateY(30px)';
    
    setTimeout(() => {
      category.style.transition = 'all 0.6s ease';
      category.style.opacity = '1';
      category.style.transform = 'translateY(0)';
    }, index * 150);
  });
}

function initializeParameterInteractions() {
  const parameterItems = document.querySelectorAll('.parameter-item');
  
  parameterItems.forEach(item => {
    // Add hover effects
    item.addEventListener('mouseenter', function() {
      this.style.backgroundColor = 'rgba(74, 144, 226, 0.1)';
      this.style.transform = 'translateX(5px)';
    });
    
    item.addEventListener('mouseleave', function() {
      this.style.backgroundColor = 'transparent';
      this.style.transform = 'translateX(0)';
    });
    
    // Add click ripple effect
    item.addEventListener('click', function(e) {
      createRippleEffect(e, this);
    });
  });
}

function createRippleEffect(event, element) {
  const ripple = document.createElement('div');
  const rect = element.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  const x = event.clientX - rect.left - size / 2;
  const y = event.clientY - rect.top - size / 2;
  
  ripple.style.cssText = `
    position: absolute;
    width: ${size}px;
    height: ${size}px;
    left: ${x}px;
    top: ${y}px;
    background: radial-gradient(circle, rgba(74, 144, 226, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    transform: scale(0);
    animation: ripple 0.6s ease-out;
    z-index: 1;
  `;
  
  // Add ripple animation keyframes if not already added
  if (!document.querySelector('#ripple-keyframes')) {
    const style = document.createElement('style');
    style.id = 'ripple-keyframes';
    style.textContent = `
      @keyframes ripple {
        to {
          transform: scale(2);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  }
  
  element.style.position = 'relative';
  element.style.overflow = 'hidden';
  element.appendChild(ripple);
  
  setTimeout(() => {
    ripple.remove();
  }, 600);
}

function initializeScrollAnimations() {
  // Intersection Observer for scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in');
        
        // Animate parameter items with stagger
        if (entry.target.classList.contains('parameter-category')) {
          const items = entry.target.querySelectorAll('.parameter-item');
          items.forEach((item, index) => {
            setTimeout(() => {
              item.style.opacity = '1';
              item.style.transform = 'translateX(0)';
            }, index * 50);
          });
        }
      }
    });
  }, observerOptions);
  
  // Observe all animatable elements
  const animatableElements = document.querySelectorAll('.parameter-category, .highlight-item');
  animatableElements.forEach(el => {
    observer.observe(el);
    
    // Set initial state for parameter items
    if (el.classList.contains('parameter-category')) {
      const items = el.querySelectorAll('.parameter-item');
      items.forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-20px)';
        item.style.transition = 'all 0.3s ease';
      });
    }
  });
}

// Category switching functionality
function initializeCategorySwitching() {
  const categories = document.querySelectorAll('.parameter-category');
  
  categories.forEach(category => {
    const header = category.querySelector('.category-header');
    
    header.addEventListener('click', function() {
      // Toggle active state
      const isActive = category.classList.contains('active');
      
      // Remove active from all categories
      categories.forEach(cat => cat.classList.remove('active'));
      
      // Add active to clicked category if it wasn't active
      if (!isActive) {
        category.classList.add('active');
        
        // Animate parameter list
        const parameterList = category.querySelector('.parameter-list');
        parameterList.style.maxHeight = parameterList.scrollHeight + 'px';
      }
    });
  });
}

// Highlight key specifications
function highlightKeySpecs() {
  const keySpecs = [
    { selector: '.param-value.highlight', delay: 0 },
    { selector: '.param-value.supported', delay: 100 }
  ];
  
  keySpecs.forEach(spec => {
    setTimeout(() => {
      const elements = document.querySelectorAll(spec.selector);
      elements.forEach(el => {
        el.style.animation = 'pulse 1s ease-in-out';
      });
    }, spec.delay);
  });
}

// Add pulse animation
const pulseStyle = document.createElement('style');
pulseStyle.textContent = `
  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }
`;
document.head.appendChild(pulseStyle);

// Initialize highlight on scroll
window.addEventListener('scroll', function() {
  const parametersSection = document.querySelector('.parameters-section');
  if (parametersSection) {
    const rect = parametersSection.getBoundingClientRect();
    const isVisible = rect.top < window.innerHeight && rect.bottom > 0;
    
    if (isVisible && !parametersSection.classList.contains('highlighted')) {
      parametersSection.classList.add('highlighted');
      setTimeout(highlightKeySpecs, 500);
    }
  }
});

// Performance optimization: Throttle scroll events
function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
}

// Apply throttling to scroll events
window.addEventListener('scroll', throttle(function() {
  // Scroll-based animations can be added here
}, 100));

// Initialize parameter tabs functionality
function initializeParameterTabs() {
  const tabs = document.querySelectorAll('.parameter-tab');
  const categories = document.querySelectorAll('.parameter-category');

  tabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const targetCategory = this.getAttribute('data-category');

      // Remove active class from all tabs
      tabs.forEach(t => t.classList.remove('active'));

      // Remove active class from all categories
      categories.forEach(c => c.classList.remove('active'));

      // Add active class to clicked tab
      this.classList.add('active');

      // Show target category
      const targetElement = document.querySelector(`.parameter-category[data-category="${targetCategory}"]`);
      if (targetElement) {
        targetElement.classList.add('active');

        // Add entrance animation
        targetElement.style.opacity = '0';
        targetElement.style.transform = 'translateY(20px)';

        setTimeout(() => {
          targetElement.style.transition = 'all 0.4s ease';
          targetElement.style.opacity = '1';
          targetElement.style.transform = 'translateY(0)';
        }, 50);
      }

      console.log(`🔄 Switched to parameter category: ${targetCategory}`);
    });
  });

  console.log('📋 Parameter tabs initialized');
}

console.log('✅ IR3 Parameters Showcase fully loaded');
