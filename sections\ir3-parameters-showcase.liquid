{% comment %}
  IR3 V2 Parameters Showcase Section
  File: sections/ir3-parameters-showcase.liquid
{% endcomment %}

{{ 'ir3-parameters-showcase.css' | asset_url | stylesheet_tag }}

<section
  class="parameters-section"
  id="parameters-{{ section.id }}"
  style="margin-top: {{ section.settings.margin_top }}px; margin-bottom: {{ section.settings.margin_bottom }}px;"
>
  <!-- Background Layers -->
  <div class="background-layer">
    <div class="gradient-overlay"></div>
    <div class="animated-gradient"></div>
    <div class="grid-pattern"></div>
    <div class="tech-lines"></div>
    <div class="floating-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>
  </div>

  <!-- Content Layer -->
  <div class="content-layer">
    <div class="container">
      <!-- Section Header -->
      <div class="section-header animate-fade-in">
        <span class="pre-title">Technical Excellence</span>
        <h2 class="main-title">
          {{ section.settings.main_title | default: 'Technical Specifications' }}
        </h2>
        <p class="section-description">
          {{ section.settings.description | default: 'Discover the advanced engineering and precision technology that powers the IR3 V2 3D printer.' }}
        </p>
      </div>

      <!-- Parameters Navigation -->
      <div class="parameters-nav">
        <div class="parameter-tab active" data-category="machine">
          <span>Machine Parameters</span>
        </div>
        <div class="parameter-tab" data-category="sensors">
          <span>Smart Sensors</span>
        </div>
        <div class="parameter-tab" data-category="electrical">
          <span>Electrical Hardware</span>
        </div>
        <div class="parameter-tab" data-category="software">
          <span>Software</span>
        </div>
      </div>

      <!-- Parameters Content -->
      <div class="parameters-content animate-fade-in" data-delay="0.2">

        <!-- Machine Parameters -->
        <div class="parameter-category active" data-category="machine">
          <div class="category-header">
            <div class="category-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h3>Machine Parameters</h3>
          </div>
          <div class="parameter-list">
            <div class="parameter-item">
              <span class="param-name">Printing Technology</span>
              <span class="param-value">FDM</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Machine Structure</span>
              <span class="param-value">Full metal frame</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Motion Structure</span>
              <span class="param-value">CoreXY</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Print Volume</span>
              <span class="param-value highlight">250×250×∞mm (X\*Y\*Z)</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Print Speed</span>
              <span class="param-value highlight">≤400mm/s</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Print Precision</span>
              <span class="param-value highlight">±0.1mm</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Layer Thickness</span>
              <span class="param-value">0.1-0.3mm</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Motor Type</span>
              <span class="param-value">5:1 Dual gear reduction extruder motor</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Nozzle Size</span>
              <span class="param-value">Standard 0.4mm</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Maximum Nozzle Temperature</span>
              <span class="param-value">300°C</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Maximum Heated Bed Temperature</span>
              <span class="param-value">90°C</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Compatible Materials</span>
              <span class="param-value">PLA/PETG/TPU/ABS/ASA, etc.</span>
            </div>
          </div>
        </div>

        <!-- Sensors -->
        <div class="parameter-category" data-category="sensors">
          <div class="category-header">
            <div class="category-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                <path d="M12 1V3" stroke="currentColor" stroke-width="2"/>
                <path d="M12 21V23" stroke="currentColor" stroke-width="2"/>
                <path d="M4.22 4.22L5.64 5.64" stroke="currentColor" stroke-width="2"/>
                <path d="M18.36 18.36L19.78 19.78" stroke="currentColor" stroke-width="2"/>
                <path d="M1 12H3" stroke="currentColor" stroke-width="2"/>
                <path d="M21 12H23" stroke="currentColor" stroke-width="2"/>
                <path d="M4.22 19.78L5.64 18.36" stroke="currentColor" stroke-width="2"/>
                <path d="M18.36 5.64L19.78 4.22" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h3>Smart Sensors</h3>
          </div>
          <div class="parameter-list">
            <div class="parameter-item">
              <span class="param-name">Vibration Compensation</span>
              <span class="param-value supported">✅ Supported</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Filament Runout Detection</span>
              <span class="param-value supported">✅ Supported</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Material Shortage Detection</span>
              <span class="param-value supported">✅ Supported</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Clogging Detection</span>
              <span class="param-value supported">✅ Supported</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Auto Leveling</span>
              <span class="param-value supported">✅ Supported</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">LED Lighting</span>
              <span class="param-value supported">✅ Supported</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Camera</span>
              <span class="param-value supported">✅ Supported</span>
            </div>
          </div>
        </div>

        <!-- Electrical Hardware -->
        <div class="parameter-category" data-category="electrical">
          <div class="category-header">
            <div class="category-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h3>Electrical Hardware</h3>
          </div>
          <div class="parameter-list">
            <div class="parameter-item">
              <span class="param-name">Input Voltage</span>
              <span class="param-value">110VAC/220VAC, 50/60Hz</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Maximum Power</span>
              <span class="param-value">800W</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Main Controller</span>
              <span class="param-value">64-bit 1.5GHz Quad-core</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Memory</span>
              <span class="param-value">16GB-SD, 1GB DDR3</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">User Interface</span>
              <span class="param-value">4.3" touchscreen 800×480</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Firmware</span>
              <span class="param-value highlight">Klipper</span>
            </div>
          </div>
        </div>

        <!-- Software -->
        <div class="parameter-category" data-category="software">
          <div class="category-header">
            <div class="category-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                <polyline points="16,18 22,12 16,6" stroke="currentColor" stroke-width="2"/>
                <polyline points="8,6 2,12 8,18" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h3>Software</h3>
          </div>
          <div class="parameter-list">
            <div class="parameter-item">
              <span class="param-name">Slicing Software</span>
              <span class="param-value">Ideamaker/Ideaformer Cura</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Input Formats</span>
              <span class="param-value">.stl/.obj/.3mf/.step/.stp/.iges/.igs/.oltp/.jpg/.jpeg/.png/.bmp</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Operating Systems</span>
              <span class="param-value">Windows/MacOS/Linux</span>
            </div>
            <div class="parameter-item">
              <span class="param-name">Output File Format</span>
              <span class="param-value">.gcode</span>
            </div>
          </div>
        </div>

      </div>

    </div>
  </div>
</section>

<script src="{{ 'ir3-parameters-showcase.js' | asset_url }}" defer></script>

{% schema %}
{
  "name": "IR3 Parameters Showcase",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "main_title",
      "label": "Main Title",
      "default": "Technical Specifications"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "Discover the advanced engineering and precision technology that powers the IR3 V2 3D printer."
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": -200,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Top margin",
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "min": -200,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Bottom margin",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "IR3 Parameters Showcase"
    }
  ]
}
{% endschema %}
